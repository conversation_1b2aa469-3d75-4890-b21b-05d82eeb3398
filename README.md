# 免费图片视频下载器

一个功能强大的免费图片和视频下载工具，支持从多个知名平台搜索和下载高质量的免费媒体资源。

## 🌟 主要特性

- **多平台支持**: 集成 Unsplash、Pixabay、Pexels 等知名免费媒体平台
- **智能搜索**: 支持按关键词和类别搜索图片和视频
- **批量下载**: 支持选择多个文件进行批量下载
- **实时预览**: 下载前可预览图片和视频缩略图
- **进度显示**: 实时显示下载进度和状态
- **错误处理**: 完善的网络错误处理和用户友好的提示
- **自动重试**: 网络异常时自动重试下载
- **文件管理**: 智能文件命名和存储管理

## 📋 系统要求

- Python 3.7 或更高版本
- Windows / macOS / Linux
- 网络连接

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥

1. 复制 `.env.example` 文件为 `.env`
2. 在 `.env` 文件中填入您的API密钥

```bash
cp .env.example .env
```

编辑 `.env` 文件:

```env
# Unsplash API密钥 (免费，需要注册)
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# Pixabay API密钥 (免费，需要注册)
PIXABAY_API_KEY=your_pixabay_api_key_here

# Pexels API密钥 (免费，需要注册)
PEXELS_API_KEY=your_pexels_api_key_here
```

### 3. 获取API密钥

#### Unsplash
1. 访问 [Unsplash Developers](https://unsplash.com/developers)
2. 注册账户并创建应用
3. 获取 Access Key

#### Pixabay
1. 访问 [Pixabay API](https://pixabay.com/api/docs/)
2. 注册账户
3. 在账户设置中获取API Key

#### Pexels
1. 访问 [Pexels API](https://www.pexels.com/api/)
2. 注册账户并创建应用
3. 获取API Key

### 4. 运行程序

```bash
python main.py
```

## 📖 使用说明

### 搜索媒体

1. **输入关键词**: 在搜索框中输入您要搜索的关键词
2. **选择类型**: 选择"图片"或"视频"
3. **选择类别**: 可选择特定类别来缩小搜索范围
4. **点击搜索**: 点击搜索按钮或按回车键开始搜索

### 预览和选择

1. **查看结果**: 搜索结果会显示在列表中，包含标题、来源、类型等信息
2. **预览媒体**: 双击任意项目可打开预览窗口
3. **选择文件**: 单击项目前的复选框来选择要下载的文件
4. **批量选择**: 使用"全选"和"取消全选"按钮

### 下载文件

1. **设置路径**: 在下载设置中选择保存路径
2. **开始下载**: 点击"下载选中"或"下载全部"
3. **查看进度**: 状态栏会显示下载进度
4. **打开文件夹**: 下载完成后可直接打开下载文件夹

## 🛠️ 项目结构

```
image_Video_Down/
├── main.py                 # 主程序入口
├── main_gui.py             # 主界面
├── api_services.py         # API服务模块
├── download_manager.py     # 下载管理器
├── preview_window.py       # 预览窗口
├── error_handler.py        # 错误处理模块
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── .env.example          # 环境变量示例
├── README.md             # 说明文档
└── logs/                 # 日志文件夹
```

## 🔧 配置选项

### 下载设置

- **默认下载路径**: `~/Downloads/MediaDownloader`
- **支持的图片格式**: JPG, PNG, WebP
- **支持的视频格式**: MP4, MOV, AVI, WebM
- **每页结果数**: 20个

### API设置

- **请求超时**: 10秒
- **重试次数**: 3次
- **请求间隔**: 0.1秒（避免API限制）

## 🐛 故障排除

### 常见问题

1. **搜索无结果**
   - 检查网络连接
   - 确认API密钥配置正确
   - 尝试更换搜索关键词

2. **下载失败**
   - 检查下载路径权限
   - 确认磁盘空间充足
   - 检查网络连接稳定性

3. **API错误**
   - 检查API密钥是否有效
   - 确认API使用额度未超限
   - 查看日志文件获取详细错误信息

### 日志文件

程序会在 `logs/` 目录下生成日志文件，包含详细的运行信息和错误记录。

## 📝 许可证

本项目仅供学习和个人使用。请遵守各API平台的使用条款和版权规定。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如果您遇到问题或有建议，请创建Issue或联系开发者。

---

**注意**: 使用本软件下载的媒体文件请遵守相关版权法律法规，仅用于合法用途。
