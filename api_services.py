"""
API服务模块 - 处理各种免费图片和视频API的调用
"""
import requests
import time
from typing import List, Dict, Optional
from config import *
from error_handler import <PERSON>rror<PERSON>and<PERSON>, RetryManager

class MediaAPI:
    """媒体API基类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MediaDownloader/1.0'
        })
    
    def search_images(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索图片 - 子类需要实现"""
        raise NotImplementedError
    
    def search_videos(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索视频 - 子类需要实现"""
        raise NotImplementedError

class UnsplashAPI(MediaAPI):
    """Unsplash API服务"""
    
    def __init__(self):
        super().__init__()
        self.api_key = UNSPLASH_ACCESS_KEY
        self.base_url = UNSPLASH_BASE_URL
        
        if self.api_key:
            self.session.headers.update({
                'Authorization': f'Client-ID {self.api_key}'
            })
    
    def search_images(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索Unsplash图片"""
        if not self.api_key:
            return []
        
        try:
            search_query = f"{query} {category}".strip()
            params = {
                'query': search_query,
                'page': page,
                'per_page': MAX_RESULTS_PER_PAGE,
                'orientation': 'all'
            }
            
            response = self.session.get(f'{self.base_url}/search/photos', params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            for item in data.get('results', []):
                results.append({
                    'id': item['id'],
                    'title': item.get('description') or item.get('alt_description', '无标题'),
                    'thumbnail': item['urls']['thumb'],
                    'preview': item['urls']['small'],
                    'download_url': item['urls']['full'],
                    'author': item['user']['name'],
                    'source': 'Unsplash',
                    'type': 'image',
                    'width': item['width'],
                    'height': item['height']
                })
            
            return results
            
        except Exception as e:
            print(f"Unsplash API错误: {e}")
            return []

class PixabayAPI(MediaAPI):
    """Pixabay API服务"""
    
    def __init__(self):
        super().__init__()
        self.api_key = PIXABAY_API_KEY
        self.base_url = PIXABAY_BASE_URL
    
    def search_images(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索Pixabay图片"""
        if not self.api_key:
            return []
        
        try:
            params = {
                'key': self.api_key,
                'q': query,
                'image_type': 'photo',
                'category': category if category else 'all',
                'page': page,
                'per_page': MAX_RESULTS_PER_PAGE,
                'safesearch': 'true'
            }
            
            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            for item in data.get('hits', []):
                results.append({
                    'id': item['id'],
                    'title': item.get('tags', '无标题'),
                    'thumbnail': item['previewURL'],
                    'preview': item['webformatURL'],
                    'download_url': item['largeImageURL'],
                    'author': item['user'],
                    'source': 'Pixabay',
                    'type': 'image',
                    'width': item['imageWidth'],
                    'height': item['imageHeight']
                })
            
            return results
            
        except Exception as e:
            print(f"Pixabay API错误: {e}")
            return []
    
    def search_videos(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索Pixabay视频"""
        if not self.api_key:
            return []
        
        try:
            params = {
                'key': self.api_key,
                'q': query,
                'video_type': 'all',
                'category': category if category else 'all',
                'page': page,
                'per_page': MAX_RESULTS_PER_PAGE,
                'safesearch': 'true'
            }
            
            response = self.session.get(f'{self.base_url}/videos/', params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            for item in data.get('hits', []):
                # 获取最高质量的视频URL
                video_url = item['videos']['large']['url'] if 'large' in item['videos'] else \
                           item['videos']['medium']['url'] if 'medium' in item['videos'] else \
                           item['videos']['small']['url']
                
                results.append({
                    'id': item['id'],
                    'title': item.get('tags', '无标题'),
                    'thumbnail': item['picture_id'],
                    'preview': video_url,
                    'download_url': video_url,
                    'author': item['user'],
                    'source': 'Pixabay',
                    'type': 'video',
                    'duration': item.get('duration', 0)
                })
            
            return results
            
        except Exception as e:
            print(f"Pixabay视频API错误: {e}")
            return []

class PexelsAPI(MediaAPI):
    """Pexels API服务"""
    
    def __init__(self):
        super().__init__()
        self.api_key = PEXELS_API_KEY
        self.base_url = PEXELS_BASE_URL
        self.video_url = PEXELS_VIDEO_URL
        
        if self.api_key:
            self.session.headers.update({
                'Authorization': self.api_key
            })
    
    def search_images(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索Pexels图片"""
        if not self.api_key:
            return []
        
        try:
            search_query = f"{query} {category}".strip()
            params = {
                'query': search_query,
                'page': page,
                'per_page': MAX_RESULTS_PER_PAGE
            }
            
            response = self.session.get(f'{self.base_url}/search', params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            for item in data.get('photos', []):
                results.append({
                    'id': item['id'],
                    'title': item.get('alt', '无标题'),
                    'thumbnail': item['src']['tiny'],
                    'preview': item['src']['medium'],
                    'download_url': item['src']['original'],
                    'author': item['photographer'],
                    'source': 'Pexels',
                    'type': 'image',
                    'width': item['width'],
                    'height': item['height']
                })
            
            return results
            
        except Exception as e:
            print(f"Pexels API错误: {e}")
            return []
    
    def search_videos(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索Pexels视频"""
        if not self.api_key:
            return []
        
        try:
            search_query = f"{query} {category}".strip()
            params = {
                'query': search_query,
                'page': page,
                'per_page': MAX_RESULTS_PER_PAGE
            }
            
            response = self.session.get(f'{self.video_url}/search', params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            for item in data.get('videos', []):
                # 获取最高质量的视频文件
                video_files = item.get('video_files', [])
                if video_files:
                    # 按质量排序，选择最高质量
                    best_video = max(video_files, key=lambda x: x.get('width', 0) * x.get('height', 0))
                    
                    results.append({
                        'id': item['id'],
                        'title': '视频',
                        'thumbnail': item['image'],
                        'preview': best_video['link'],
                        'download_url': best_video['link'],
                        'author': item['user']['name'],
                        'source': 'Pexels',
                        'type': 'video',
                        'duration': item.get('duration', 0),
                        'width': best_video.get('width', 0),
                        'height': best_video.get('height', 0)
                    })
            
            return results
            
        except Exception as e:
            print(f"Pexels视频API错误: {e}")
            return []

class MediaSearchService:
    """媒体搜索服务 - 整合所有API"""
    
    def __init__(self):
        self.apis = {
            'unsplash': UnsplashAPI(),
            'pixabay': PixabayAPI(),
            'pexels': PexelsAPI()
        }
    
    def search_all_images(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索所有平台的图片"""
        all_results = []
        
        for api_name, api in self.apis.items():
            try:
                results = api.search_images(query, category, page)
                all_results.extend(results)
                time.sleep(0.1)  # 避免API限制
            except Exception as e:
                print(f"{api_name} 搜索失败: {e}")
        
        return all_results
    
    def search_all_videos(self, query: str, category: str = '', page: int = 1) -> List[Dict]:
        """搜索所有平台的视频"""
        all_results = []
        
        # 只有Pixabay和Pexels支持视频搜索
        video_apis = ['pixabay', 'pexels']
        
        for api_name in video_apis:
            if api_name in self.apis:
                try:
                    results = self.apis[api_name].search_videos(query, category, page)
                    all_results.extend(results)
                    time.sleep(0.1)  # 避免API限制
                except Exception as e:
                    print(f"{api_name} 视频搜索失败: {e}")
        
        return all_results
