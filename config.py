"""
配置文件 - 存储API密钥和应用设置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# API配置
UNSPLASH_ACCESS_KEY = os.getenv('UNSPLASH_ACCESS_KEY', '')
PIXABAY_API_KEY = os.getenv('PIXABAY_API_KEY', '')
PEXELS_API_KEY = os.getenv('PEXELS_API_KEY', '')

# 应用设置
DEFAULT_DOWNLOAD_PATH = os.path.join(os.path.expanduser('~'), 'Downloads', 'MediaDownloader')
MAX_RESULTS_PER_PAGE = 20
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.webp']
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.mov', '.avi', '.webm']

# API端点
UNSPLASH_BASE_URL = 'https://api.unsplash.com'
PIXABAY_BASE_URL = 'https://pixabay.com/api'
PEXELS_BASE_URL = 'https://api.pexels.com/v1'
PEXELS_VIDEO_URL = 'https://api.pexels.com/videos'

# 图片类别映射
IMAGE_CATEGORIES = {
    '全部': '',
    '自然': 'nature',
    '人物': 'people',
    '动物': 'animals',
    '建筑': 'architecture',
    '商务': 'business',
    '食物': 'food',
    '旅行': 'travel',
    '科技': 'technology',
    '艺术': 'art',
    '运动': 'sports',
    '时尚': 'fashion'
}

# 视频类别映射
VIDEO_CATEGORIES = {
    '全部': '',
    '自然': 'nature',
    '人物': 'people',
    '动物': 'animals',
    '城市': 'city',
    '商务': 'business',
    '旅行': 'travel',
    '科技': 'technology',
    '运动': 'sports',
    '时尚': 'fashion',
    '抽象': 'abstract'
}
