"""
下载管理器 - 处理文件下载、进度显示和文件管理
"""
import os
import requests
import threading
from urllib.parse import urlparse
from typing import Callable, Optional
import time
from config import DEFAULT_DOWNLOAD_PATH, SUPPORTED_IMAGE_FORMATS, SUPPORTED_VIDEO_FORMATS

class DownloadManager:
    """文件下载管理器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MediaDownloader/1.0'
        })
        self.download_path = DEFAULT_DOWNLOAD_PATH
        self.ensure_download_directory()
    
    def ensure_download_directory(self):
        """确保下载目录存在"""
        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path)
    
    def set_download_path(self, path: str):
        """设置下载路径"""
        self.download_path = path
        self.ensure_download_directory()
    
    def get_file_extension(self, url: str, content_type: str = '') -> str:
        """从URL或Content-Type获取文件扩展名"""
        # 首先尝试从URL获取
        parsed_url = urlparse(url)
        path = parsed_url.path
        if '.' in path:
            ext = os.path.splitext(path)[1].lower()
            if ext in SUPPORTED_IMAGE_FORMATS + SUPPORTED_VIDEO_FORMATS:
                return ext
        
        # 从Content-Type获取
        if content_type:
            if 'image/jpeg' in content_type:
                return '.jpg'
            elif 'image/png' in content_type:
                return '.png'
            elif 'image/webp' in content_type:
                return '.webp'
            elif 'video/mp4' in content_type:
                return '.mp4'
            elif 'video/webm' in content_type:
                return '.webm'
        
        # 默认扩展名
        return '.jpg'
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename[:100]  # 限制文件名长度
    
    def generate_filename(self, media_item: dict) -> str:
        """生成文件名"""
        title = self.sanitize_filename(media_item.get('title', 'untitled'))
        source = media_item.get('source', 'unknown')
        media_id = media_item.get('id', 'unknown')
        
        # 获取文件扩展名
        url = media_item.get('download_url', '')
        ext = self.get_file_extension(url)
        
        filename = f"{title}_{source}_{media_id}{ext}"
        return filename
    
    def download_file(self, 
                     media_item: dict, 
                     progress_callback: Optional[Callable] = None,
                     complete_callback: Optional[Callable] = None,
                     error_callback: Optional[Callable] = None) -> bool:
        """下载单个文件"""
        try:
            url = media_item.get('download_url')
            if not url:
                if error_callback:
                    error_callback("下载URL为空")
                return False
            
            filename = self.generate_filename(media_item)
            filepath = os.path.join(self.download_path, filename)
            
            # 检查文件是否已存在
            if os.path.exists(filepath):
                if error_callback:
                    error_callback(f"文件已存在: {filename}")
                return False
            
            # 开始下载
            response = self.session.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 更新进度
                        if progress_callback and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            progress_callback(progress, downloaded_size, total_size)
            
            # 下载完成
            if complete_callback:
                complete_callback(filepath)
            
            return True
            
        except Exception as e:
            if error_callback:
                error_callback(f"下载失败: {str(e)}")
            return False
    
    def download_file_async(self, 
                           media_item: dict, 
                           progress_callback: Optional[Callable] = None,
                           complete_callback: Optional[Callable] = None,
                           error_callback: Optional[Callable] = None):
        """异步下载文件"""
        def download_thread():
            self.download_file(media_item, progress_callback, complete_callback, error_callback)
        
        thread = threading.Thread(target=download_thread)
        thread.daemon = True
        thread.start()
        return thread
    
    def download_multiple_files(self, 
                               media_items: list, 
                               progress_callback: Optional[Callable] = None,
                               complete_callback: Optional[Callable] = None,
                               error_callback: Optional[Callable] = None):
        """批量下载文件"""
        def batch_download():
            total_files = len(media_items)
            completed_files = 0
            failed_files = 0
            
            for i, media_item in enumerate(media_items):
                try:
                    def file_progress(progress, downloaded, total):
                        # 计算总体进度
                        overall_progress = ((completed_files + progress/100) / total_files) * 100
                        if progress_callback:
                            progress_callback(overall_progress, i+1, total_files, media_item.get('title', ''))
                    
                    def file_complete(filepath):
                        nonlocal completed_files
                        completed_files += 1
                    
                    def file_error(error_msg):
                        nonlocal failed_files
                        failed_files += 1
                        if error_callback:
                            error_callback(f"文件 {media_item.get('title', '')} 下载失败: {error_msg}")
                    
                    success = self.download_file(media_item, file_progress, file_complete, file_error)
                    if not success:
                        failed_files += 1
                    
                    # 短暂延迟避免过快请求
                    time.sleep(0.5)
                    
                except Exception as e:
                    failed_files += 1
                    if error_callback:
                        error_callback(f"下载出错: {str(e)}")
            
            # 批量下载完成
            if complete_callback:
                complete_callback(completed_files, failed_files, total_files)
        
        thread = threading.Thread(target=batch_download)
        thread.daemon = True
        thread.start()
        return thread
    
    def get_download_path(self) -> str:
        """获取当前下载路径"""
        return self.download_path
    
    def open_download_folder(self):
        """打开下载文件夹"""
        try:
            import subprocess
            import platform
            
            system = platform.system()
            if system == "Windows":
                os.startfile(self.download_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", self.download_path])
            else:  # Linux
                subprocess.run(["xdg-open", self.download_path])
        except Exception as e:
            print(f"无法打开文件夹: {e}")

# 全局下载管理器实例
download_manager = DownloadManager()
