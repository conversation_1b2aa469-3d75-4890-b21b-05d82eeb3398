"""
错误处理和日志模块
"""
import logging
import os
import traceback
from datetime import datetime
from typing import Optional, Callable
import tkinter as tk
from tkinter import messagebox

# 设置日志
def setup_logging():
    """设置日志配置"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"media_downloader_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

logger = setup_logging()

class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def handle_api_error(error: Exception, api_name: str, operation: str) -> str:
        """处理API错误"""
        error_msg = str(error)
        
        # 网络连接错误
        if "ConnectionError" in str(type(error)) or "timeout" in error_msg.lower():
            user_msg = f"网络连接失败，请检查网络连接后重试"
            logger.error(f"{api_name} {operation} 网络错误: {error_msg}")
        
        # API限制错误
        elif "429" in error_msg or "rate limit" in error_msg.lower():
            user_msg = f"{api_name} API请求过于频繁，请稍后再试"
            logger.warning(f"{api_name} {operation} API限制: {error_msg}")
        
        # 认证错误
        elif "401" in error_msg or "unauthorized" in error_msg.lower():
            user_msg = f"{api_name} API密钥无效或已过期，请检查配置"
            logger.error(f"{api_name} {operation} 认证错误: {error_msg}")
        
        # 服务器错误
        elif "500" in error_msg or "502" in error_msg or "503" in error_msg:
            user_msg = f"{api_name} 服务器暂时不可用，请稍后再试"
            logger.error(f"{api_name} {operation} 服务器错误: {error_msg}")
        
        # 其他错误
        else:
            user_msg = f"{api_name} 服务出现问题: {error_msg}"
            logger.error(f"{api_name} {operation} 未知错误: {error_msg}")
        
        return user_msg
    
    @staticmethod
    def handle_download_error(error: Exception, filename: str) -> str:
        """处理下载错误"""
        error_msg = str(error)
        
        # 网络错误
        if "ConnectionError" in str(type(error)) or "timeout" in error_msg.lower():
            user_msg = f"下载 {filename} 时网络连接失败"
            logger.error(f"下载网络错误 {filename}: {error_msg}")
        
        # 文件系统错误
        elif "Permission" in error_msg or "Access" in error_msg:
            user_msg = f"没有权限保存文件 {filename}，请检查文件夹权限"
            logger.error(f"下载权限错误 {filename}: {error_msg}")
        
        # 磁盘空间不足
        elif "No space" in error_msg or "disk full" in error_msg.lower():
            user_msg = f"磁盘空间不足，无法保存 {filename}"
            logger.error(f"下载磁盘空间错误 {filename}: {error_msg}")
        
        # HTTP错误
        elif "404" in error_msg:
            user_msg = f"文件 {filename} 不存在或已被删除"
            logger.error(f"下载404错误 {filename}: {error_msg}")
        
        elif "403" in error_msg:
            user_msg = f"没有权限下载文件 {filename}"
            logger.error(f"下载403错误 {filename}: {error_msg}")
        
        # 其他错误
        else:
            user_msg = f"下载 {filename} 失败: {error_msg}"
            logger.error(f"下载未知错误 {filename}: {error_msg}")
        
        return user_msg
    
    @staticmethod
    def log_info(message: str):
        """记录信息日志"""
        logger.info(message)
    
    @staticmethod
    def log_warning(message: str):
        """记录警告日志"""
        logger.warning(message)
    
    @staticmethod
    def log_error(message: str, exception: Optional[Exception] = None):
        """记录错误日志"""
        if exception:
            logger.error(f"{message}: {str(exception)}")
            logger.debug(traceback.format_exc())
        else:
            logger.error(message)

class UserFeedback:
    """用户反馈处理"""
    
    @staticmethod
    def show_error(parent: tk.Widget, title: str, message: str):
        """显示错误对话框"""
        messagebox.showerror(title, message, parent=parent)
    
    @staticmethod
    def show_warning(parent: tk.Widget, title: str, message: str):
        """显示警告对话框"""
        messagebox.showwarning(title, message, parent=parent)
    
    @staticmethod
    def show_info(parent: tk.Widget, title: str, message: str):
        """显示信息对话框"""
        messagebox.showinfo(title, message, parent=parent)
    
    @staticmethod
    def ask_yes_no(parent: tk.Widget, title: str, message: str) -> bool:
        """显示是/否对话框"""
        return messagebox.askyesno(title, message, parent=parent)

def safe_execute(func: Callable, error_handler: Optional[Callable] = None, *args, **kwargs):
    """安全执行函数，捕获异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        ErrorHandler.log_error(f"执行函数 {func.__name__} 时出错", e)
        if error_handler:
            error_handler(e)
        return None

class RetryManager:
    """重试管理器"""
    
    @staticmethod
    def retry_with_backoff(func: Callable, max_retries: int = 3, backoff_factor: float = 1.0, *args, **kwargs):
        """带退避的重试机制"""
        import time
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                
                wait_time = backoff_factor * (2 ** attempt)
                ErrorHandler.log_warning(f"第 {attempt + 1} 次尝试失败，{wait_time} 秒后重试: {str(e)}")
                time.sleep(wait_time)
        
        return None

class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_api_keys() -> dict:
        """验证API密钥配置"""
        from config import UNSPLASH_ACCESS_KEY, PIXABAY_API_KEY, PEXELS_API_KEY
        
        validation_result = {
            'valid_apis': [],
            'invalid_apis': [],
            'warnings': []
        }
        
        # 检查Unsplash
        if UNSPLASH_ACCESS_KEY and len(UNSPLASH_ACCESS_KEY.strip()) > 10:
            validation_result['valid_apis'].append('Unsplash')
        else:
            validation_result['invalid_apis'].append('Unsplash')
            validation_result['warnings'].append('Unsplash API密钥未配置或无效')
        
        # 检查Pixabay
        if PIXABAY_API_KEY and len(PIXABAY_API_KEY.strip()) > 10:
            validation_result['valid_apis'].append('Pixabay')
        else:
            validation_result['invalid_apis'].append('Pixabay')
            validation_result['warnings'].append('Pixabay API密钥未配置或无效')
        
        # 检查Pexels
        if PEXELS_API_KEY and len(PEXELS_API_KEY.strip()) > 10:
            validation_result['valid_apis'].append('Pexels')
        else:
            validation_result['invalid_apis'].append('Pexels')
            validation_result['warnings'].append('Pexels API密钥未配置或无效')
        
        return validation_result
    
    @staticmethod
    def validate_download_path(path: str) -> tuple[bool, str]:
        """验证下载路径"""
        if not path:
            return False, "下载路径不能为空"
        
        if not os.path.exists(path):
            try:
                os.makedirs(path, exist_ok=True)
                return True, "路径已创建"
            except Exception as e:
                return False, f"无法创建路径: {str(e)}"
        
        if not os.path.isdir(path):
            return False, "路径不是有效的文件夹"
        
        if not os.access(path, os.W_OK):
            return False, "没有写入权限"
        
        return True, "路径有效"

# 全局错误处理器实例
error_handler = ErrorHandler()
user_feedback = UserFeedback()
config_validator = ConfigValidator()
