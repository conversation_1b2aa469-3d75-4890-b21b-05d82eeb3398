#!/usr/bin/env python3
"""
免费图片视频下载器 - 主程序入口
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_gui import MediaDownloaderGUI
from error_handler import ErrorHandler, ConfigValidator, user_feedback
from config import *

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import requests
    except ImportError:
        missing_packages.append("requests")
    
    try:
        import PIL
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import cv2
    except ImportError:
        missing_packages.append("opencv-python")
    
    try:
        from dotenv import load_dotenv
    except ImportError:
        missing_packages.append("python-dotenv")
    
    if missing_packages:
        error_msg = f"缺少以下依赖包:\n{', '.join(missing_packages)}\n\n请运行以下命令安装:\npip install {' '.join(missing_packages)}"
        print(error_msg)
        
        # 如果tkinter可用，显示图形错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("依赖包缺失", error_msg)
            root.destroy()
        except:
            pass
        
        return False
    
    return True

def check_configuration():
    """检查配置"""
    validation_result = ConfigValidator.validate_api_keys()
    
    if not validation_result['valid_apis']:
        warning_msg = """警告: 没有配置任何API密钥!

要使用此软件，您需要至少配置一个API密钥:

1. 复制 .env.example 文件为 .env
2. 在 .env 文件中填入您的API密钥

支持的免费API:
• Unsplash: https://unsplash.com/developers
• Pixabay: https://pixabay.com/api/docs/
• Pexels: https://www.pexels.com/api/

没有API密钥将无法搜索和下载媒体文件。
是否继续运行程序?"""
        
        try:
            root = tk.Tk()
            root.withdraw()
            result = messagebox.askyesno("配置警告", warning_msg)
            root.destroy()
            return result
        except:
            print(warning_msg)
            return True
    
    elif validation_result['warnings']:
        info_msg = f"已配置的API: {', '.join(validation_result['valid_apis'])}\n\n"
        if validation_result['invalid_apis']:
            info_msg += f"未配置的API: {', '.join(validation_result['invalid_apis'])}\n\n"
        info_msg += "您可以配置更多API密钥来获得更多搜索结果。"
        
        ErrorHandler.log_info(f"API配置检查: {info_msg}")
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        DEFAULT_DOWNLOAD_PATH,
        "logs"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            ErrorHandler.log_error(f"创建目录失败 {directory}", e)

def main():
    """主函数"""
    print("=" * 50)
    print("免费图片视频下载器 v1.0")
    print("=" * 50)
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        print("依赖检查失败，程序退出")
        sys.exit(1)
    print("✓ 依赖包检查通过")
    
    # 创建目录
    print("创建必要目录...")
    create_directories()
    print("✓ 目录创建完成")
    
    # 检查配置
    print("检查API配置...")
    if not check_configuration():
        print("用户取消运行")
        sys.exit(0)
    print("✓ 配置检查完成")
    
    # 启动GUI
    print("启动图形界面...")
    try:
        app = MediaDownloaderGUI()
        ErrorHandler.log_info("应用程序启动")
        app.run()
        ErrorHandler.log_info("应用程序正常退出")
    except Exception as e:
        error_msg = f"启动应用程序时发生错误: {str(e)}"
        ErrorHandler.log_error(error_msg, e)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            print(error_msg)
        
        sys.exit(1)

if __name__ == "__main__":
    main()
