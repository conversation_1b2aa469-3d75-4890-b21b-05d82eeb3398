"""
主用户界面 - 使用tkinter创建图形界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from PIL import Image, ImageTk
import requests
from io import BytesIO
import os

from api_services import MediaSearchService
from download_manager import download_manager
from config import IMAGE_CATEGORIES, VIDEO_CATEGORIES
from preview_window import show_preview

class MediaDownloaderGUI:
    """媒体下载器主界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("免费图片视频下载器")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 初始化服务
        self.search_service = MediaSearchService()
        self.current_results = []
        self.selected_items = []
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 设置样式
        self.setup_styles()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 搜索框架
        self.search_frame = ttk.LabelFrame(self.main_frame, text="搜索设置", padding=10)
        
        # 搜索关键词
        ttk.Label(self.search_frame, text="搜索关键词:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, width=30)
        self.search_entry.grid(row=0, column=1, padx=(0, 10))
        
        # 媒体类型选择
        ttk.Label(self.search_frame, text="媒体类型:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        self.media_type_var = tk.StringVar(value="图片")
        self.media_type_combo = ttk.Combobox(self.search_frame, textvariable=self.media_type_var, 
                                           values=["图片", "视频"], state="readonly", width=10)
        self.media_type_combo.grid(row=0, column=3, padx=(0, 10))
        self.media_type_combo.bind('<<ComboboxSelected>>', self.on_media_type_change)
        
        # 类别选择
        ttk.Label(self.search_frame, text="类别:").grid(row=1, column=0, sticky="w", padx=(0, 5), pady=(10, 0))
        self.category_var = tk.StringVar(value="全部")
        self.category_combo = ttk.Combobox(self.search_frame, textvariable=self.category_var, 
                                         values=list(IMAGE_CATEGORIES.keys()), state="readonly", width=15)
        self.category_combo.grid(row=1, column=1, pady=(10, 0), padx=(0, 10))
        
        # 搜索按钮
        self.search_btn = ttk.Button(self.search_frame, text="搜索", command=self.search_media)
        self.search_btn.grid(row=1, column=2, pady=(10, 0), padx=(0, 10))
        
        # 下载设置框架
        self.download_frame = ttk.LabelFrame(self.main_frame, text="下载设置", padding=10)
        
        # 下载路径
        ttk.Label(self.download_frame, text="下载路径:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.download_path_var = tk.StringVar(value=download_manager.get_download_path())
        self.download_path_entry = ttk.Entry(self.download_frame, textvariable=self.download_path_var, width=50)
        self.download_path_entry.grid(row=0, column=1, padx=(0, 5))
        
        self.browse_btn = ttk.Button(self.download_frame, text="浏览", command=self.browse_download_path)
        self.browse_btn.grid(row=0, column=2, padx=(0, 5))
        
        self.open_folder_btn = ttk.Button(self.download_frame, text="打开文件夹", command=self.open_download_folder)
        self.open_folder_btn.grid(row=0, column=3)
        
        # 结果显示框架
        self.results_frame = ttk.LabelFrame(self.main_frame, text="搜索结果", padding=5)
        
        # 创建Treeview显示结果
        columns = ("标题", "来源", "类型", "尺寸", "作者")
        self.results_tree = ttk.Treeview(self.results_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题和宽度
        self.results_tree.heading("#0", text="选择")
        self.results_tree.column("#0", width=50, minwidth=50)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == "标题":
                self.results_tree.column(col, width=300, minwidth=200)
            elif col == "来源":
                self.results_tree.column(col, width=80, minwidth=80)
            elif col == "类型":
                self.results_tree.column(col, width=60, minwidth=60)
            elif col == "尺寸":
                self.results_tree.column(col, width=100, minwidth=100)
            elif col == "作者":
                self.results_tree.column(col, width=150, minwidth=100)
        
        # 添加滚动条
        self.results_scrollbar = ttk.Scrollbar(self.results_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=self.results_scrollbar.set)
        
        # 绑定事件
        self.results_tree.bind('<Double-1>', self.on_item_double_click)
        self.results_tree.bind('<Button-1>', self.on_item_click)
        
        # 操作按钮框架
        self.action_frame = ttk.Frame(self.main_frame)
        
        self.select_all_btn = ttk.Button(self.action_frame, text="全选", command=self.select_all)
        self.select_none_btn = ttk.Button(self.action_frame, text="取消全选", command=self.select_none)
        self.preview_btn = ttk.Button(self.action_frame, text="预览选中", command=self.preview_selected)
        self.download_selected_btn = ttk.Button(self.action_frame, text="下载选中", command=self.download_selected)
        self.download_all_btn = ttk.Button(self.action_frame, text="下载全部", command=self.download_all)
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var, maximum=100)
        
        # 绑定回车键搜索
        self.search_entry.bind('<Return>', lambda e: self.search_media())
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 搜索框架
        self.search_frame.pack(fill="x", pady=(0, 10))
        
        # 下载设置框架
        self.download_frame.pack(fill="x", pady=(0, 10))
        
        # 结果显示框架
        self.results_frame.pack(fill="both", expand=True, pady=(0, 10))
        self.results_tree.pack(side="left", fill="both", expand=True)
        self.results_scrollbar.pack(side="right", fill="y")
        
        # 操作按钮框架
        self.action_frame.pack(fill="x", pady=(0, 10))
        self.select_all_btn.pack(side="left", padx=(0, 5))
        self.select_none_btn.pack(side="left", padx=(0, 5))
        self.preview_btn.pack(side="left", padx=(0, 5))
        self.download_selected_btn.pack(side="left", padx=(0, 5))
        self.download_all_btn.pack(side="left")
        
        # 状态栏
        self.status_frame.pack(fill="x")
        self.status_label.pack(side="left")
        self.progress_bar.pack(side="right", fill="x", expand=True, padx=(10, 0))
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
    
    def on_media_type_change(self, event=None):
        """媒体类型改变时更新类别选项"""
        media_type = self.media_type_var.get()
        if media_type == "图片":
            categories = list(IMAGE_CATEGORIES.keys())
        else:
            categories = list(VIDEO_CATEGORIES.keys())
        
        self.category_combo['values'] = categories
        self.category_var.set("全部")
    
    def search_media(self):
        """搜索媒体"""
        query = self.search_var.get().strip()
        if not query:
            messagebox.showwarning("警告", "请输入搜索关键词")
            return
        
        # 在新线程中执行搜索
        threading.Thread(target=self._search_thread, args=(query,), daemon=True).start()
    
    def _search_thread(self, query):
        """搜索线程"""
        try:
            self.status_var.set("正在搜索...")
            self.progress_var.set(0)
            
            media_type = self.media_type_var.get()
            category = self.category_var.get()
            
            # 获取类别映射
            if media_type == "图片":
                category_map = IMAGE_CATEGORIES
                results = self.search_service.search_all_images(query, category_map.get(category, ''))
            else:
                category_map = VIDEO_CATEGORIES
                results = self.search_service.search_all_videos(query, category_map.get(category, ''))
            
            self.current_results = results
            
            # 在主线程中更新UI
            self.root.after(0, self._update_results_ui)
            
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"搜索失败: {str(e)}"))
    
    def _update_results_ui(self):
        """更新结果显示"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # 添加新结果
        for i, result in enumerate(self.current_results):
            size_info = ""
            if result.get('width') and result.get('height'):
                size_info = f"{result['width']}x{result['height']}"
            elif result.get('duration'):
                size_info = f"{result['duration']}s"
            
            self.results_tree.insert("", "end", iid=str(i), text="☐", values=(
                result.get('title', '无标题')[:50],
                result.get('source', '未知'),
                result.get('type', '未知'),
                size_info,
                result.get('author', '未知')
            ))
        
        self.status_var.set(f"找到 {len(self.current_results)} 个结果")
        self.progress_var.set(100)
    
    def on_item_click(self, event):
        """处理项目点击"""
        item = self.results_tree.selection()[0] if self.results_tree.selection() else None
        if item:
            # 切换选择状态
            current_text = self.results_tree.item(item, "text")
            if current_text == "☐":
                self.results_tree.item(item, text="☑")
            else:
                self.results_tree.item(item, text="☐")
    
    def on_item_double_click(self, event):
        """处理项目双击 - 预览"""
        item = self.results_tree.selection()[0] if self.results_tree.selection() else None
        if item:
            index = int(item)
            if 0 <= index < len(self.current_results):
                self.preview_item(self.current_results[index])
    
    def get_selected_items(self):
        """获取选中的项目"""
        selected = []
        for item in self.results_tree.get_children():
            if self.results_tree.item(item, "text") == "☑":
                index = int(item)
                if 0 <= index < len(self.current_results):
                    selected.append(self.current_results[index])
        return selected
    
    def select_all(self):
        """全选"""
        for item in self.results_tree.get_children():
            self.results_tree.item(item, text="☑")
    
    def select_none(self):
        """取消全选"""
        for item in self.results_tree.get_children():
            self.results_tree.item(item, text="☐")
    
    def preview_selected(self):
        """预览选中项目"""
        selected = self.get_selected_items()
        if not selected:
            messagebox.showwarning("警告", "请先选择要预览的项目")
            return
        
        # 预览第一个选中的项目
        self.preview_item(selected[0])
    
    def preview_item(self, item):
        """预览单个项目"""
        show_preview(self.root, item)
    
    def browse_download_path(self):
        """浏览下载路径"""
        path = filedialog.askdirectory(initialdir=self.download_path_var.get())
        if path:
            self.download_path_var.set(path)
            download_manager.set_download_path(path)
    
    def open_download_folder(self):
        """打开下载文件夹"""
        download_manager.open_download_folder()
    
    def download_selected(self):
        """下载选中项目"""
        selected = self.get_selected_items()
        if not selected:
            messagebox.showwarning("警告", "请先选择要下载的项目")
            return
        
        self._start_download(selected)
    
    def download_all(self):
        """下载全部项目"""
        if not self.current_results:
            messagebox.showwarning("警告", "没有可下载的项目")
            return
        
        self._start_download(self.current_results)
    
    def _start_download(self, items):
        """开始下载"""
        # 更新下载路径
        download_manager.set_download_path(self.download_path_var.get())
        
        def progress_callback(progress, current, total, filename):
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.status_var.set(f"下载中 ({current}/{total}): {filename}"))
        
        def complete_callback(completed, failed, total):
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_var.set(f"下载完成: 成功 {completed}, 失败 {failed}, 总计 {total}"))
            self.root.after(0, lambda: messagebox.showinfo("下载完成", f"下载完成!\n成功: {completed}\n失败: {failed}\n总计: {total}"))
        
        def error_callback(error_msg):
            self.root.after(0, lambda: messagebox.showerror("下载错误", error_msg))
        
        # 开始批量下载
        download_manager.download_multiple_files(items, progress_callback, complete_callback, error_callback)
        self.status_var.set(f"开始下载 {len(items)} 个文件...")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MediaDownloaderGUI()
    app.run()
