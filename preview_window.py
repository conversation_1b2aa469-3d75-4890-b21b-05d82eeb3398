"""
预览窗口 - 显示图片和视频预览
"""
import tkinter as tk
from tkinter import ttk
import requests
from PIL import Image, ImageTk
from io import BytesIO
import threading
import webbrowser

class PreviewWindow:
    """媒体预览窗口"""
    
    def __init__(self, parent, media_item):
        self.parent = parent
        self.media_item = media_item
        self.window = tk.Toplevel(parent)
        self.window.title(f"预览 - {media_item.get('title', '无标题')}")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 设置窗口图标和属性
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.load_preview()
    
    def create_widgets(self):
        """创建预览窗口组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.window)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 信息框架
        self.info_frame = ttk.LabelFrame(self.main_frame, text="媒体信息", padding=10)
        self.info_frame.pack(fill="x", pady=(0, 10))
        
        # 显示媒体信息
        info_text = f"""标题: {self.media_item.get('title', '无标题')}
来源: {self.media_item.get('source', '未知')}
作者: {self.media_item.get('author', '未知')}
类型: {self.media_item.get('type', '未知')}"""
        
        if self.media_item.get('width') and self.media_item.get('height'):
            info_text += f"\n尺寸: {self.media_item['width']} x {self.media_item['height']}"
        
        if self.media_item.get('duration'):
            info_text += f"\n时长: {self.media_item['duration']} 秒"
        
        self.info_label = ttk.Label(self.info_frame, text=info_text, justify="left")
        self.info_label.pack(anchor="w")
        
        # 预览框架
        self.preview_frame = ttk.LabelFrame(self.main_frame, text="预览", padding=10)
        self.preview_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 预览内容区域
        self.preview_canvas = tk.Canvas(self.preview_frame, bg="white")
        self.preview_scrollbar_v = ttk.Scrollbar(self.preview_frame, orient="vertical", command=self.preview_canvas.yview)
        self.preview_scrollbar_h = ttk.Scrollbar(self.preview_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=self.preview_scrollbar_v.set, xscrollcommand=self.preview_scrollbar_h.set)
        
        self.preview_canvas.pack(side="left", fill="both", expand=True)
        self.preview_scrollbar_v.pack(side="right", fill="y")
        self.preview_scrollbar_h.pack(side="bottom", fill="x")
        
        # 加载状态标签
        self.status_label = ttk.Label(self.preview_frame, text="正在加载预览...")
        self.status_label.place(relx=0.5, rely=0.5, anchor="center")
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill="x")
        
        # 按钮
        self.download_btn = ttk.Button(self.button_frame, text="下载此项", command=self.download_item)
        self.download_btn.pack(side="left", padx=(0, 5))
        
        self.open_source_btn = ttk.Button(self.button_frame, text="在浏览器中打开", command=self.open_in_browser)
        self.open_source_btn.pack(side="left", padx=(0, 5))
        
        self.close_btn = ttk.Button(self.button_frame, text="关闭", command=self.window.destroy)
        self.close_btn.pack(side="right")
    
    def load_preview(self):
        """加载预览内容"""
        media_type = self.media_item.get('type', '')
        
        if media_type == 'image':
            threading.Thread(target=self._load_image_preview, daemon=True).start()
        elif media_type == 'video':
            self._show_video_info()
        else:
            self.status_label.config(text="无法预览此类型的媒体")
    
    def _load_image_preview(self):
        """加载图片预览"""
        try:
            preview_url = self.media_item.get('preview') or self.media_item.get('thumbnail')
            if not preview_url:
                self.window.after(0, lambda: self.status_label.config(text="没有可用的预览图片"))
                return
            
            # 下载图片
            response = requests.get(preview_url, timeout=10)
            response.raise_for_status()
            
            # 加载图片
            image = Image.open(BytesIO(response.content))
            
            # 调整图片大小以适应窗口
            canvas_width = 600
            canvas_height = 400
            
            img_width, img_height = image.size
            scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)
            
            if scale < 1.0:
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(image)
            
            # 在主线程中更新UI
            self.window.after(0, lambda: self._display_image(photo))
            
        except Exception as e:
            self.window.after(0, lambda: self.status_label.config(text=f"加载预览失败: {str(e)}"))
    
    def _display_image(self, photo):
        """显示图片"""
        self.status_label.place_forget()
        
        # 清空画布
        self.preview_canvas.delete("all")
        
        # 显示图片
        self.preview_canvas.create_image(
            self.preview_canvas.winfo_width() // 2,
            self.preview_canvas.winfo_height() // 2,
            anchor="center",
            image=photo
        )
        
        # 保持图片引用
        self.preview_canvas.image = photo
        
        # 更新滚动区域
        self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
    
    def _show_video_info(self):
        """显示视频信息"""
        self.status_label.config(text="视频预览不可用\n请点击'在浏览器中打开'查看视频")
        
        # 如果有缩略图，尝试显示
        thumbnail_url = self.media_item.get('thumbnail')
        if thumbnail_url:
            threading.Thread(target=self._load_video_thumbnail, daemon=True).start()
    
    def _load_video_thumbnail(self):
        """加载视频缩略图"""
        try:
            thumbnail_url = self.media_item.get('thumbnail')
            if not thumbnail_url:
                return
            
            response = requests.get(thumbnail_url, timeout=10)
            response.raise_for_status()
            
            image = Image.open(BytesIO(response.content))
            
            # 调整大小
            canvas_width = 400
            canvas_height = 300
            
            img_width, img_height = image.size
            scale = min(canvas_width / img_width, canvas_height / img_height, 1.0)
            
            if scale < 1.0:
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            photo = ImageTk.PhotoImage(image)
            
            self.window.after(0, lambda: self._display_video_thumbnail(photo))
            
        except Exception as e:
            print(f"加载视频缩略图失败: {e}")
    
    def _display_video_thumbnail(self, photo):
        """显示视频缩略图"""
        # 清空画布
        self.preview_canvas.delete("all")
        
        # 显示缩略图
        self.preview_canvas.create_image(
            self.preview_canvas.winfo_width() // 2,
            self.preview_canvas.winfo_height() // 2,
            anchor="center",
            image=photo
        )
        
        # 添加播放按钮图标（文字）
        self.preview_canvas.create_text(
            self.preview_canvas.winfo_width() // 2,
            self.preview_canvas.winfo_height() // 2,
            text="▶",
            font=("Arial", 48),
            fill="white",
            tags="play_button"
        )
        
        # 保持图片引用
        self.preview_canvas.image = photo
        
        # 更新状态
        self.status_label.config(text="点击'在浏览器中打开'播放视频")
    
    def download_item(self):
        """下载当前项目"""
        from download_manager import download_manager
        
        def progress_callback(progress, downloaded, total):
            self.window.after(0, lambda: self.download_btn.config(text=f"下载中... {progress:.1f}%"))
        
        def complete_callback(filepath):
            self.window.after(0, lambda: self.download_btn.config(text="下载完成"))
            self.window.after(0, lambda: tk.messagebox.showinfo("下载完成", f"文件已保存到:\n{filepath}"))
        
        def error_callback(error_msg):
            self.window.after(0, lambda: self.download_btn.config(text="下载此项"))
            self.window.after(0, lambda: tk.messagebox.showerror("下载失败", error_msg))
        
        self.download_btn.config(text="准备下载...")
        download_manager.download_file_async(self.media_item, progress_callback, complete_callback, error_callback)
    
    def open_in_browser(self):
        """在浏览器中打开"""
        url = self.media_item.get('preview') or self.media_item.get('download_url')
        if url:
            webbrowser.open(url)
        else:
            tk.messagebox.showwarning("警告", "没有可用的URL")

def show_preview(parent, media_item):
    """显示预览窗口"""
    PreviewWindow(parent, media_item)
