#!/usr/bin/env python3
"""
快速启动脚本 - 简化版本用于测试
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_basic_imports():
    """检查基本导入"""
    try:
        print("检查基本模块...")
        import tkinter as tk
        print("✓ tkinter 可用")
        
        import requests
        print("✓ requests 可用")
        
        from PIL import Image
        print("✓ Pillow 可用")
        
        return True
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False

def check_config():
    """检查配置"""
    try:
        print("\n检查配置...")
        from config import PEXELS_API_KEY, DEFAULT_DOWNLOAD_PATH
        
        if PEXELS_API_KEY:
            print(f"✓ Pexels API密钥已配置: {PEXELS_API_KEY[:10]}...")
        else:
            print("⚠️ Pexels API密钥未配置")
        
        print(f"✓ 默认下载路径: {DEFAULT_DOWNLOAD_PATH}")
        
        return True
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    try:
        print("\n测试API连接...")
        from api_services import PexelsAPI
        
        api = PexelsAPI()
        print("✓ Pexels API实例创建成功")
        
        # 简单测试
        results = api.search_images("cat", "", 1)
        if results:
            print(f"✓ API连接正常，找到 {len(results)} 个结果")
            return True
        else:
            print("⚠️ API返回空结果，但连接正常")
            return True
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("免费图片视频下载器 - 快速启动测试")
    print("=" * 50)
    
    # 检查基本导入
    if not check_basic_imports():
        print("\n❌ 基本模块检查失败，请安装依赖包:")
        print("pip install -r requirements.txt")
        return
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败")
        return
    
    # 测试API连接
    if not test_api_connection():
        print("\n⚠️ API连接测试失败，但程序仍可启动")
    
    print("\n" + "=" * 50)
    print("启动主程序...")
    print("=" * 50)
    
    try:
        from main_gui import MediaDownloaderGUI
        print("✓ 主程序模块导入成功")
        
        print("正在启动GUI界面...")
        app = MediaDownloaderGUI()
        app.run()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
