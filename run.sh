#!/bin/bash

echo "================================================"
echo "免费图片视频下载器"
echo "================================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.7或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "检查Python版本..."
$PYTHON_CMD --version

echo
echo "检查依赖包..."
if ! $PYTHON_CMD -c "import requests" &> /dev/null; then
    echo "正在安装依赖包..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
else
    echo "依赖包已安装"
fi

echo
echo "启动应用程序..."
$PYTHON_CMD main.py

echo
echo "程序已退出"
