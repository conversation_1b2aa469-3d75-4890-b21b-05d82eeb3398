#!/usr/bin/env python3
"""
API连接测试脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api_services import PexelsAPI, MediaSearchService
from config import PEXELS_API_KEY

def test_pexels_api():
    """测试Pexels API连接"""
    print("=" * 50)
    print("测试Pexels API连接")
    print("=" * 50)
    
    if not PEXELS_API_KEY:
        print("❌ Pexels API密钥未配置")
        return False
    
    print(f"✓ API密钥已配置: {PEXELS_API_KEY[:10]}...")
    
    try:
        # 创建API实例
        api = PexelsAPI()
        print("✓ API实例创建成功")
        
        # 测试图片搜索
        print("\n测试图片搜索...")
        results = api.search_images("nature", "", 1)
        
        if results:
            print(f"✓ 图片搜索成功，找到 {len(results)} 个结果")
            
            # 显示第一个结果的信息
            if len(results) > 0:
                first_result = results[0]
                print(f"  - 标题: {first_result.get('title', '无标题')}")
                print(f"  - 作者: {first_result.get('author', '未知')}")
                print(f"  - 尺寸: {first_result.get('width', 0)}x{first_result.get('height', 0)}")
                print(f"  - 来源: {first_result.get('source', '未知')}")
        else:
            print("⚠️ 图片搜索返回空结果")
        
        # 测试视频搜索
        print("\n测试视频搜索...")
        video_results = api.search_videos("ocean", "", 1)
        
        if video_results:
            print(f"✓ 视频搜索成功，找到 {len(video_results)} 个结果")
            
            # 显示第一个结果的信息
            if len(video_results) > 0:
                first_video = video_results[0]
                print(f"  - 标题: {first_video.get('title', '无标题')}")
                print(f"  - 作者: {first_video.get('author', '未知')}")
                print(f"  - 时长: {first_video.get('duration', 0)} 秒")
                print(f"  - 尺寸: {first_video.get('width', 0)}x{first_video.get('height', 0)}")
        else:
            print("⚠️ 视频搜索返回空结果")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_media_search_service():
    """测试媒体搜索服务"""
    print("\n" + "=" * 50)
    print("测试媒体搜索服务")
    print("=" * 50)
    
    try:
        service = MediaSearchService()
        print("✓ 媒体搜索服务创建成功")
        
        # 测试搜索所有图片
        print("\n测试搜索所有平台图片...")
        all_images = service.search_all_images("cat", "animals", 1)
        
        if all_images:
            print(f"✓ 找到 {len(all_images)} 张图片")
            
            # 按来源分组显示
            sources = {}
            for img in all_images:
                source = img.get('source', '未知')
                if source not in sources:
                    sources[source] = 0
                sources[source] += 1
            
            for source, count in sources.items():
                print(f"  - {source}: {count} 张")
        else:
            print("⚠️ 没有找到图片")
        
        # 测试搜索所有视频
        print("\n测试搜索所有平台视频...")
        all_videos = service.search_all_videos("nature", "nature", 1)
        
        if all_videos:
            print(f"✓ 找到 {len(all_videos)} 个视频")
            
            # 按来源分组显示
            sources = {}
            for video in all_videos:
                source = video.get('source', '未知')
                if source not in sources:
                    sources[source] = 0
                sources[source] += 1
            
            for source, count in sources.items():
                print(f"  - {source}: {count} 个")
        else:
            print("⚠️ 没有找到视频")
        
        return True
        
    except Exception as e:
        print(f"❌ 媒体搜索服务测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("免费图片视频下载器 - API连接测试")
    print("当前时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 测试Pexels API
    pexels_success = test_pexels_api()
    
    # 测试媒体搜索服务
    service_success = test_media_search_service()
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    if pexels_success:
        print("✓ Pexels API连接正常")
    else:
        print("❌ Pexels API连接失败")
    
    if service_success:
        print("✓ 媒体搜索服务正常")
    else:
        print("❌ 媒体搜索服务异常")
    
    if pexels_success and service_success:
        print("\n🎉 所有测试通过！您可以正常使用应用程序了。")
        print("\n启动应用程序:")
        print("  python main.py")
        print("  或双击 run.bat (Windows)")
        print("  或运行 ./run.sh (Linux/macOS)")
    else:
        print("\n⚠️ 部分测试失败，请检查网络连接和API配置。")

if __name__ == "__main__":
    main()
