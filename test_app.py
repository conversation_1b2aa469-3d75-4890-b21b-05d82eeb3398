#!/usr/bin/env python3
"""
应用程序测试脚本
"""
import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api_services import MediaSearchService, UnsplashAPI, PixabayAPI, PexelsAPI
from download_manager import DownloadManager
from error_handler import ErrorHandler, ConfigValidator

class TestAPIServices(unittest.TestCase):
    """测试API服务"""
    
    def setUp(self):
        self.search_service = MediaSearchService()
    
    def test_media_search_service_init(self):
        """测试媒体搜索服务初始化"""
        self.assertIsInstance(self.search_service, MediaSearchService)
        self.assertIn('unsplash', self.search_service.apis)
        self.assertIn('pixabay', self.search_service.apis)
        self.assertIn('pexels', self.search_service.apis)
    
    @patch('requests.Session.get')
    def test_unsplash_api_search_images(self, mock_get):
        """测试Unsplash图片搜索"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.json.return_value = {
            'results': [
                {
                    'id': 'test123',
                    'description': 'Test Image',
                    'urls': {
                        'thumb': 'https://example.com/thumb.jpg',
                        'small': 'https://example.com/small.jpg',
                        'full': 'https://example.com/full.jpg'
                    },
                    'user': {'name': 'Test User'},
                    'width': 1920,
                    'height': 1080
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        api = UnsplashAPI()
        # 设置测试API密钥
        api.api_key = 'test_key'
        
        results = api.search_images('nature', 'landscape')
        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['id'], 'test123')
        self.assertEqual(results[0]['source'], 'Unsplash')
        self.assertEqual(results[0]['type'], 'image')

class TestDownloadManager(unittest.TestCase):
    """测试下载管理器"""
    
    def setUp(self):
        self.download_manager = DownloadManager()
    
    def test_download_manager_init(self):
        """测试下载管理器初始化"""
        self.assertIsInstance(self.download_manager, DownloadManager)
        self.assertTrue(os.path.exists(self.download_manager.download_path))
    
    def test_sanitize_filename(self):
        """测试文件名清理"""
        test_cases = [
            ('normal_file.jpg', 'normal_file.jpg'),
            ('file<with>invalid:chars.jpg', 'file_with_invalid_chars.jpg'),
            ('file/with\\path|chars?.jpg', 'file_with_path_chars_.jpg'),
            ('very_long_filename_' * 10 + '.jpg', 'very_long_filename_very_long_filename_very_long_filename_very_long_filename_very_long_fi.jpg')
        ]
        
        for input_name, expected in test_cases:
            result = self.download_manager.sanitize_filename(input_name)
            self.assertEqual(result, expected)
    
    def test_get_file_extension(self):
        """测试文件扩展名获取"""
        test_cases = [
            ('https://example.com/image.jpg', '', '.jpg'),
            ('https://example.com/image.png', '', '.png'),
            ('https://example.com/video.mp4', '', '.mp4'),
            ('https://example.com/file', 'image/jpeg', '.jpg'),
            ('https://example.com/file', 'video/mp4', '.mp4'),
            ('https://example.com/unknown', '', '.jpg')  # 默认
        ]
        
        for url, content_type, expected in test_cases:
            result = self.download_manager.get_file_extension(url, content_type)
            self.assertEqual(result, expected)

class TestErrorHandler(unittest.TestCase):
    """测试错误处理器"""
    
    def test_handle_api_error(self):
        """测试API错误处理"""
        # 测试网络错误
        error = Exception("ConnectionError: Network unreachable")
        result = ErrorHandler.handle_api_error(error, "TestAPI", "search")
        self.assertIn("网络连接失败", result)
        
        # 测试API限制错误
        error = Exception("429 Too Many Requests")
        result = ErrorHandler.handle_api_error(error, "TestAPI", "search")
        self.assertIn("请求过于频繁", result)
        
        # 测试认证错误
        error = Exception("401 Unauthorized")
        result = ErrorHandler.handle_api_error(error, "TestAPI", "search")
        self.assertIn("API密钥无效", result)
    
    def test_handle_download_error(self):
        """测试下载错误处理"""
        # 测试网络错误
        error = Exception("ConnectionError: timeout")
        result = ErrorHandler.handle_download_error(error, "test.jpg")
        self.assertIn("网络连接失败", result)
        
        # 测试权限错误
        error = Exception("Permission denied")
        result = ErrorHandler.handle_download_error(error, "test.jpg")
        self.assertIn("权限", result)
        
        # 测试404错误
        error = Exception("404 Not Found")
        result = ErrorHandler.handle_download_error(error, "test.jpg")
        self.assertIn("不存在", result)

class TestConfigValidator(unittest.TestCase):
    """测试配置验证器"""
    
    def test_validate_download_path(self):
        """测试下载路径验证"""
        # 测试空路径
        valid, message = ConfigValidator.validate_download_path("")
        self.assertFalse(valid)
        self.assertIn("不能为空", message)
        
        # 测试有效路径
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            valid, message = ConfigValidator.validate_download_path(temp_dir)
            self.assertTrue(valid)

def run_basic_functionality_test():
    """运行基本功能测试"""
    print("=" * 50)
    print("基本功能测试")
    print("=" * 50)
    
    # 测试配置验证
    print("1. 测试配置验证...")
    validation_result = ConfigValidator.validate_api_keys()
    print(f"   有效API: {validation_result['valid_apis']}")
    print(f"   无效API: {validation_result['invalid_apis']}")
    
    # 测试下载管理器
    print("2. 测试下载管理器...")
    dm = DownloadManager()
    print(f"   下载路径: {dm.get_download_path()}")
    print(f"   路径存在: {os.path.exists(dm.get_download_path())}")
    
    # 测试文件名处理
    print("3. 测试文件名处理...")
    test_filename = "测试<文件>名称:包含|特殊?字符*.jpg"
    clean_filename = dm.sanitize_filename(test_filename)
    print(f"   原文件名: {test_filename}")
    print(f"   清理后: {clean_filename}")
    
    # 测试媒体搜索服务
    print("4. 测试媒体搜索服务...")
    search_service = MediaSearchService()
    print(f"   已初始化API数量: {len(search_service.apis)}")
    
    print("✓ 基本功能测试完成")

def main():
    """主测试函数"""
    print("免费图片视频下载器 - 测试套件")
    
    # 运行基本功能测试
    run_basic_functionality_test()
    
    print("\n" + "=" * 50)
    print("单元测试")
    print("=" * 50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)

if __name__ == "__main__":
    main()
