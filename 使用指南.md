# 免费图片视频下载器 - 使用指南

## 🎯 您的配置状态

✅ **Pexels API已配置**: 您已经成功配置了Pexels API密钥，可以搜索和下载Pexels平台的图片和视频。

⚠️ **其他API未配置**: Unsplash和Pixabay API密钥尚未配置，如需更多搜索结果，可以配置这些API。

## 🚀 立即开始使用

### 方法1: 直接运行Python脚本
```bash
python main.py
```

### 方法2: 使用启动脚本
- **Windows**: 双击 `run.bat`
- **Linux/macOS**: 运行 `./run.sh`

### 方法3: 快速启动（推荐用于测试）
```bash
python quick_start.py
```

## 📖 详细使用步骤

### 1. 启动程序
运行上述任一启动方法，程序会打开图形界面。

### 2. 搜索媒体文件

#### 搜索图片
1. 在"搜索关键词"框中输入您要搜索的内容，例如：
   - `cat`（猫）
   - `nature`（自然）
   - `sunset`（日落）
   - `coffee`（咖啡）

2. 选择"媒体类型"为"图片"

3. 选择类别（可选）：
   - 全部、自然、人物、动物、建筑、商务、食物、旅行、科技、艺术、运动、时尚

4. 点击"搜索"按钮

#### 搜索视频
1. 输入搜索关键词
2. 选择"媒体类型"为"视频"
3. 选择相应类别
4. 点击"搜索"

### 3. 预览和选择

#### 查看搜索结果
- 搜索结果会显示在下方的列表中
- 每个结果显示：标题、来源、类型、尺寸、作者

#### 预览媒体
- **双击**任意结果项可打开预览窗口
- 预览窗口显示：
  - 媒体详细信息
  - 图片预览（实际图片）
  - 视频缩略图
  - 下载和浏览器打开选项

#### 选择要下载的文件
- **单击**结果项前的复选框来选择/取消选择
- 使用"全选"按钮选择所有结果
- 使用"取消全选"按钮取消所有选择

### 4. 下载文件

#### 设置下载路径
1. 在"下载路径"框中查看当前路径
2. 点击"浏览"按钮选择新的下载文件夹
3. 点击"打开文件夹"可直接打开下载目录

#### 开始下载
1. **下载选中**: 只下载您选中的文件
2. **下载全部**: 下载所有搜索结果

#### 监控下载进度
- 状态栏显示当前下载状态
- 进度条显示下载进度
- 下载完成后会显示成功/失败统计

## 💡 使用技巧

### 搜索技巧
1. **使用英文关键词**: API主要支持英文搜索，效果更好
2. **组合关键词**: 如 `mountain sunset`、`cat playing`
3. **选择合适类别**: 可以获得更精准的结果
4. **尝试不同关键词**: 如果结果不理想，尝试同义词

### 下载技巧
1. **预览后下载**: 建议先预览确认质量再下载
2. **批量下载**: 选择多个文件一次性下载更高效
3. **检查存储空间**: 确保有足够的磁盘空间
4. **网络稳定**: 在网络稳定时进行大批量下载

### 文件管理
- 下载的文件会自动命名为：`标题_来源_ID.扩展名`
- 支持的图片格式：JPG, PNG, WebP
- 支持的视频格式：MP4, MOV, AVI, WebM
- 重复文件会自动跳过

## 🔧 故障排除

### 搜索无结果
1. 检查网络连接
2. 尝试更换关键词
3. 检查API密钥配置

### 下载失败
1. 检查下载路径权限
2. 确认网络连接稳定
3. 检查磁盘空间
4. 查看错误提示信息

### 程序启动失败
1. 确认Python版本（需要3.7+）
2. 安装依赖包：`pip install -r requirements.txt`
3. 检查.env文件配置

## 📊 当前可用功能

### ✅ 已配置的服务
- **Pexels**: 图片和视频搜索下载

### ⚪ 可扩展的服务
如需更多搜索结果，可以配置以下API：

#### Unsplash（图片）
1. 访问：https://unsplash.com/developers
2. 注册并创建应用
3. 在.env文件中添加：`UNSPLASH_ACCESS_KEY=您的密钥`

#### Pixabay（图片和视频）
1. 访问：https://pixabay.com/api/docs/
2. 注册账户
3. 在.env文件中添加：`PIXABAY_API_KEY=您的密钥`

## 🎉 开始使用

现在您可以启动程序开始搜索和下载免费的高质量图片和视频了！

```bash
python main.py
```

祝您使用愉快！ 🚀
